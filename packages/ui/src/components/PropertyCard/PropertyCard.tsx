'use client';

import Image from 'next/image';

import {
  currencyFormatter,
  dateFormatter,
  removeUtmParams,
} from '@qantasexperiences/utils/formatting';

import { PropertyAndTripadvisorRating } from '../Rating';
import { Link } from './../Link';
import { PropertyCardSash } from './PropertyCardSash';

interface Props {
  address: {
    suburb: string;
  };
  availabilitySearchUrl: string;
  isExternal?: boolean;
  mainImage: {
    caption: string;
    urlOriginal: string;
  };
  name: string;
  offer: {
    cancellation: { percentage?: string }[];
    charges: {
      currency: string;
      total: { amount: number };
    };
    pointsEarned: {
      qffPoints: number;
    };
    promotion?: string | null;
  };
  rating: {
    type: 'AAA' | 'SELF_RATED';
    value: number;
  };
  tripadvisor: {
    averageRating: number;
    imageUrl?: string;
    reviewCount: number;
  };
}

const PropertyCard = (props: Props) => {
  const url = new URL(props.availabilitySearchUrl);
  const start = new Date(url.searchParams.get('checkIn') ?? new Date());
  const end = new Date(url.searchParams.get('checkOut') ?? new Date());
  // Some browsers use different whitespace characters so we need to replace them to avoid hydration issues.
  const dateRange = dateFormatter.formatRange(start, end).replace(/\s/g, ' ');

  const price = currencyFormatter.format(props.offer.charges.total.amount).substring(1);

  const pointsEarned = Math.round(
    props.offer.pointsEarned.qffPoints / props.offer.charges.total.amount,
  );

  return (
    <div className="hover:shadow-floating relative grid grid-rows-1 place-items-stretch gap-y-2 overflow-clip rounded border border-neutral-100 bg-white shadow transition-shadow supports-[grid-template-rows:subgrid]:row-span-3 supports-[grid-template-rows:subgrid]:grid-rows-subgrid">
      <PropertyCardSash>{props.offer.promotion}</PropertyCardSash>
      <Image
        src={props.mainImage.urlOriginal}
        alt={props.mainImage.caption}
        unoptimized={true}
        width={0}
        height={0}
        className="col-span-full row-end-1 min-h-36 w-auto object-cover"
      />
      <div className="px-4 pt-1">
        <h3 className="text-xl font-semibold">
          <Link
            href={removeUtmParams(url)}
            isExternal={props.isExternal ?? false}
            className="no-underline after:absolute after:inset-0 hover:no-underline focus-visible:no-underline active:no-underline"
            iconPosition="none"
            iconName={undefined}
            isMuted
          >
            {props.name}
          </Link>
        </h3>
        <p className="mb-1 text-base text-neutral-600">{props.address.suburb}</p>
        <PropertyAndTripadvisorRating
          rating={{ rating: props.rating.value, ratingType: props.rating.type }}
          tripadvisor={{
            rating: props.tripadvisor.averageRating,
            reviewCount: props.tripadvisor.reviewCount,
          }}
        />
      </div>
      <div className="px-4 pb-4 text-end">
        <p className="text-neutral-600">
          {dateRange} <span aria-hidden>•</span> 1 night from
        </p>
        <p className="mb-2 leading-none font-semibold">
          {props.offer.charges.currency} $ <span className="align-middle text-[2rem]">{price}</span>
        </p>
        <p>
          <svg width={20} height={20} className="inline-block">
            <path
              d="M19.842 19.15c.***************.05.017.033 0 .05 0 .083-.034s.033-.102 0-.136c-1.984-2.113-4.417-3.82-7.134-4.92l-.833-.338a1.183 1.183 0 0 1-.717-1.099c.05-1.809 4.25-1.437 4.684-2.3l.066-.152a7.75 7.75 0 0 0-3.017-1.64c-.016.051-.05.254.184.643.25.406-.267 1.065-1.034.355l-.067-.05C6.54 4.321 3.973 7.735.19 1.717c-.034-.05-.084-.068-.134-.034s-.066.085-.05.135C2.99 9.106 9.091 7.568 9.807 14.365a.553.553 0 0 0 .467.49 20.138 20.138 0 0 1 9.551 4.295h.017Z"
              fill="#E40000"
            />
          </svg>{' '}
          Earn{' '}
          <span className="font-semibold">
            {pointsEarned} <abbr title="Qantas Points">PTS</abbr>
          </span>{' '}
          per $1 spent^
        </p>
        {props.offer.cancellation[0]?.percentage === '100%' && (
          <p className="font-semibold text-[#0F7401]">
            <svg width="24" height="24" className="inline-block align-bottom">
              <path d="M 18.471 12.301 L 19.234 15.147 L 18.069 14.475 C 17.178 17.021 14.76 18.847 11.91 18.847 C 9.524 18.847 7.434 17.564 6.298 15.65 L 7.537 15.319 C 8.493 16.709 10.095 17.623 11.91 17.623 C 14.307 17.623 16.332 16.033 16.987 13.85 L 15.627 13.066 L 18.471 12.301 Z M 12.484 8.589 L 12.484 9.481 C 13.337 9.691 13.765 10.334 13.794 11.036 L 12.892 11.036 C 12.868 10.526 12.597 10.179 11.873 10.179 C 11.184 10.179 10.771 10.488 10.771 10.933 C 10.771 11.321 11.068 11.569 11.995 11.809 C 12.919 12.05 13.911 12.444 13.911 13.605 C 13.911 14.44 13.278 14.899 12.484 15.051 L 12.484 15.929 L 11.261 15.929 L 11.261 15.042 C 10.478 14.875 9.81 14.374 9.763 13.481 L 10.656 13.481 C 10.701 13.961 11.031 14.337 11.873 14.337 C 12.773 14.337 12.972 13.887 12.972 13.607 C 12.972 13.228 12.769 12.869 11.75 12.624 C 10.611 12.352 9.836 11.882 9.836 10.941 C 9.836 10.154 10.469 9.64 11.261 9.47 L 11.261 8.589 L 12.484 8.589 Z M 11.91 5.8 C 14.088 5.8 16.015 6.867 17.2 8.508 L 15.914 8.853 C 14.943 7.732 13.51 7.023 11.91 7.023 C 9.757 7.023 7.903 8.309 7.073 10.153 L 8.376 10.906 L 5.423 11.696 L 4.634 8.747 L 6.009 9.54 C 7.053 7.329 9.304 5.8 11.91 5.8 Z" />
            </svg>
            Free cancellation
          </p>
        )}
        {/* <p className="font-semibold text-neutral-600">
            <svg
              width="16"
              height="16"
              className="inline-block align-[-.2rem]"
            >
              <path d="M7.333 10h1.334v1.334H7.333V10Zm0-5.333h1.334v4H7.333v-4Zm.66-3.333A6.663 6.663 0 0 0 1.333 8c0 3.68 2.98 6.667 6.66 6.667A6.67 6.67 0 0 0 14.667 8a6.67 6.67 0 0 0-6.674-6.667Zm.007 12A5.332 5.332 0 0 1 2.667 8 5.332 5.332 0 0 1 8 2.667 5.332 5.332 0 0 1 13.333 8 5.332 5.332 0 0 1 8 13.334Z" />
            </svg>{' '}
            Non-refundable
          </p> */}
        {/* <p className="font-semibold">
            <svg
              width="16"
              height="16"
              className="inline-block align-[-.2rem]"
            >
              <path d="M7.333 10h1.334v1.334H7.333V10Zm0-5.333h1.334v4H7.333v-4Zm.66-3.333A6.663 6.663 0 0 0 1.333 8c0 3.68 2.98 6.667 6.66 6.667A6.67 6.67 0 0 0 14.667 8a6.67 6.67 0 0 0-6.674-6.667Zm.007 12A5.332 5.332 0 0 1 2.667 8 5.332 5.332 0 0 1 8 2.667 5.332 5.332 0 0 1 13.333 8 5.332 5.332 0 0 1 8 13.334Z" />
            </svg>{' '}
            Cancellation policy
          </p> */}
      </div>
    </div>
  );
};

export { PropertyCard };
