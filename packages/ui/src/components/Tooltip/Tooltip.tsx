'use client';

import type { ForwardedRef, ReactNode } from 'react';
import type { TooltipTriggerProps } from 'react-aria';
import { forwardRef, useId } from 'react';
import { TooltipTrigger } from 'react-aria-components';

import type { TooltipButtonProps, TooltipContentProps } from './components';
import { TooltipArrow, TooltipButton, TooltipContent } from './components';

export interface TooltipProps
  extends Omit<TooltipContentProps, 'children' | 'className' | 'style'>,
    Omit<TooltipTriggerProps, 'children'>,
    TooltipButtonProps {
  children?: ReactNode;
  includeArrow?: boolean;
  tooltipClassName?: TooltipContentProps['className'];
  tooltipContent?: ReactNode;
  tooltipStyle?: TooltipContentProps['style'];
}

const Tooltip = (
  {
    children,
    variant = 'primary',
    placement,
    includeArrow = true,
    // Trigger props
    isDisabled,
    delay = 0,
    closeDelay = 0,
    trigger,
    isOpen,
    defaultOpen,
    onOpenChange,
    // Tooltip props
    tooltipContent,
    triggerRef,
    isEntering,
    isExiting,
    UNSTABLE_portalContainer,
    containerPadding,
    offset,
    crossOffset,
    shouldFlip,
    arrowBoundaryOffset,
    tooltipClassName,
    tooltipStyle,
    isDismissible,
    onDismiss,
    ...rest
  }: TooltipProps,
  forwardedRef: ForwardedRef<HTMLButtonElement>,
) => {
  const tooltipTriggerProps = {
    isDisabled,
    delay,
    closeDelay,
    trigger,
    isOpen,
    defaultOpen,
    onOpenChange,
  } satisfies TooltipTriggerProps;

  const tooltipProps = {
    triggerRef,
    isEntering,
    isExiting,
    UNSTABLE_portalContainer,
    containerPadding,
    offset,
    crossOffset,
    shouldFlip,
    arrowBoundaryOffset,
    className: tooltipClassName,
    style: tooltipStyle,
    isDismissible,
    onDismiss,
  } satisfies TooltipContentProps;

  const descId = useId();

  return (
    <TooltipTrigger {...tooltipTriggerProps}>
      <TooltipButton
        isDisabled={isDisabled}
        aria-describedby={tooltipContent ? descId : rest['aria-describedby']}
        {...rest}
        ref={forwardedRef}
      >
        {children}
      </TooltipButton>
      {tooltipContent && (
        <>
          <span id={descId} hidden>
            {typeof tooltipContent === 'string' ? tooltipContent : undefined}
          </span>
          <TooltipContent placement={placement} variant={variant} {...tooltipProps}>
            {includeArrow && <TooltipArrow placement={placement} variant={variant} />}
            {tooltipContent}
          </TooltipContent>
        </>
      )}
    </TooltipTrigger>
  );
};

const ForwardedTooltip = forwardRef(Tooltip);
export { ForwardedTooltip as Tooltip };
