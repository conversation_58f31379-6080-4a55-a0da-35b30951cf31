import type { ImageProps } from 'next/image';
import Image from 'next/image';

import type { Tenant } from '@qantasexperiences/tenants';
import { cn } from '@qantasexperiences/utils/tailwind';

// Assets
import jetstarHolidaysInverse from './assets/jetstar-holidays-inverse.svg?url';
import jetstarHolidays from './assets/jetstar-holidays.svg?url';
import jetstarInverse from './assets/jetstar-inverse.svg?url';
import jetstar from './assets/jetstar.svg?url';
import qantasHolidaysDesktopInverse from './assets/qantas-holidays-desktop-inverse.svg?url';
import qantasHolidaysDesktop from './assets/qantas-holidays-desktop.svg?url';
import qantasHolidaysInverse from './assets/qantas-holidays-inverse.svg?url';
import qantasHolidays from './assets/qantas-holidays.svg?url';
import qantasHotelsDesktopInverse from './assets/qantas-hotels-desktop-inverse.svg?url';
import qantasHotelsDesktop from './assets/qantas-hotels-desktop.svg?url';
import qantasHotelsInverse from './assets/qantas-hotels-inverse.svg?url';
import qantasHotels from './assets/qantas-hotels.svg?url';

// Provide a decorative default. Use role="presentation" instead of aria-hidden for broader AT support.
// Allow consumer to override alt to provide a meaningful label when needed.
const common = { alt: '', role: 'presentation', loading: 'eager' } as const;

type TenantLogoProps = {
  inverse?: boolean;
  tenant: Tenant;
} & (Omit<ImageProps, 'src' | 'alt'> & Partial<Pick<ImageProps, 'alt'>>);

export const TenantLogo = ({ tenant: { brand, channel }, inverse, ...rest }: TenantLogoProps) => {
  const p = { ...common, ...rest };

  // JETSTAR Holidays
  if (channel === 'holidays' && brand === 'jetstar' && inverse)
    return <Image src={jetstarHolidaysInverse} {...p} />;
  if (channel === 'holidays' && brand === 'jetstar') return <Image src={jetstarHolidays} {...p} />;

  // JETSTAR
  if (brand === 'jetstar' && inverse) return <Image src={jetstarInverse} {...p} />;
  if (brand === 'jetstar') return <Image src={jetstar} {...p} />;

  const mp = { ...p, className: cn('lg:hidden', p.className) };
  const dp = { ...p, className: cn('hidden lg:block', p.className) };

  // QANTAS HOTELS
  if (channel === 'hotels' && inverse) {
    return (
      <>
        <Image src={qantasHotelsInverse} {...mp} />
        <Image src={qantasHotelsDesktopInverse} {...dp} />
      </>
    );
  } else if (channel === 'hotels') {
    return (
      <>
        <Image src={qantasHotels} {...mp} />
        <Image src={qantasHotelsDesktop} {...dp} />
      </>
    );
  }

  // QANTAS HOLIDAYS
  if (inverse) {
    return (
      <>
        <Image src={qantasHolidaysInverse} {...mp} />
        <Image src={qantasHolidaysDesktopInverse} {...dp} />
      </>
    );
  }

  return (
    <>
      <Image src={qantasHolidays} {...mp} />
      <Image src={qantasHolidaysDesktop} {...dp} />
    </>
  );
};
