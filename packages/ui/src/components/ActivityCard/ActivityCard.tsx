'use client';

import type { FC } from 'react';

import { cn } from '@qantasexperiences/utils/tailwind';

import { Card } from '../Card';
import { ContentfulImage } from '../ContentfulImage';
import { Divider } from '../Divider';
import { Rating } from '../Rating';
import { SvgIcon } from '../SvgIcon';

export interface ActivityCardProps {
  activityCategory?: string;
  activityDuration?: string;
  activityTitle: string;
  cancellationPolicy?: string;
  className?: string;
  imageUrl: string;
  pointsEarnRate?: number;
  priceLeading: number;
  priceLeadingUnit?: 'person' | 'group' | 'package';
  priceOriginal?: number;
  pricePoints?: number;
  rating?: number;
  reviewCount?: number;
}

export const ActivityCard: FC<ActivityCardProps> = ({
  activityCategory,
  activityDuration,
  activityTitle,
  imageUrl,
  rating = 0.0,
  reviewCount = 0,
  priceLeading,
  priceLeadingUnit = 'person',
  priceOriginal,
  pricePoints,
  pointsEarnRate = 3,
  cancellationPolicy,
  className,
}: ActivityCardProps) => {
  return (
    <Card className={cn('max-w-[360px]', className)}>
      <ContentfulImage
        src={imageUrl}
        alt={activityTitle}
        height={160}
        width={300}
        style={{ width: '100%', height: '160px' }}
      />
      <Card.Body className="flex flex-col justify-between">
        <div>
          <h3 className="mb-2 line-clamp-2 text-lg font-bold">{activityTitle}</h3>

          {/* eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing */}
          {(activityCategory || activityDuration) && (
            <div
              className="mb-1 flex flex-row items-center text-sm text-neutral-600"
              data-testid="activity-details"
            >
              {activityCategory}
              {activityCategory && activityDuration && (
                <Divider className="mx-2" color="standard" orientation="vertical" variant="solid" />
              )}
              {activityDuration && (
                <>
                  <SvgIcon name="clock" className="mr-1 h-4 w-4 fill-neutral-500" aria-hidden />
                  {activityDuration}
                </>
              )}
            </div>
          )}
          <div className="text-sm text-neutral-600">
            <Rating rating={rating} reviewCount={reviewCount} ratingType="star" size="medium" />
          </div>
        </div>

        <div className="mt-4">
          <div>
            <p className="text-md">Per {priceLeadingUnit} from</p>
            <p
              className="leading-tight"
              style={{ fontSize: '26px' }}
              aria-label={`$${priceLeading}`}
            >
              <strong>
                <sup className="text-md" style={{ top: '-0.2em', paddingRight: '0.1em' }}>
                  $
                </sup>
                <span data-testid="price-leading">{priceLeading.toLocaleString('en-AU')}</span>
                <sup className="text-md" style={{ top: '-0.2em', paddingLeft: '0.1em' }}>
                  *
                </sup>
              </strong>
            </p>
            {pricePoints && (
              <p className="text-md" aria-label={`or ${pricePoints} points ^`}>
                <strong>
                  or {pricePoints.toLocaleString('en-AU')} <abbr title="Qantas Points">PTS</abbr>^
                </strong>
              </p>
            )}
            {priceOriginal && (
              <div className="text-md mt-1 flex flex-row items-center">
                <div className="rounded-md bg-green-50 px-2 py-0 font-bold text-green-800 uppercase">
                  Save ${(priceOriginal - priceLeading).toLocaleString('en-AU')}
                </div>
                <p className="ml-2 text-neutral-600 line-through">
                  Was ${priceOriginal.toLocaleString('en-AU')}
                </p>
              </div>
            )}
          </div>

          <div className="text-md mt-4">
            {/* Points earn rate */}
            <span
              className="mt-1 flex flex-row items-center"
              aria-label={`Earn ${pointsEarnRate} points per $1 spent ^`}
            >
              <SvgIcon name="roo" className="mr-2 h-5 w-5 fill-red-600" aria-hidden />
              <p>
                Earn{' '}
                <strong>
                  {pointsEarnRate} <abbr title="Qantas Points">PTS</abbr>
                </strong>{' '}
                per $1 spent^
              </p>
            </span>

            {/* Cancellation Policy */}
            {cancellationPolicy && (
              <span className="mt-1 flex flex-row items-center">
                <SvgIcon
                  name="dollar-with-rotating-arrows"
                  className="mr-2 h-5 w-5 fill-green-600"
                  aria-hidden
                />
                <p className="text-green-600">
                  <strong>{cancellationPolicy}</strong>
                </p>
              </span>
            )}
          </div>
        </div>
      </Card.Body>
    </Card>
  );
};
