import { useId, useRef } from 'react';
import Link from 'next/link';

import type { NavigationCollection } from '../../../../../types/navigation';
import { SvgIcon } from '../../../../SvgIcon';
import { NavigationItem } from './NavigationItem';

export const NavigationItemSubList = ({ item }: { item: NavigationCollection }) => {
  const ob = useRef<HTMLButtonElement>(null);
  const container = useRef<HTMLUListElement>(null);
  const panelId = useId();

  const open = () => {
    ob.current?.setAttribute('aria-expanded', 'true');
    const el = container.current?.querySelector('a,button');
    if (el && el instanceof HTMLElement) el.focus();
  };

  const close = () => {
    ob.current?.setAttribute('aria-expanded', 'false');
    ob.current?.focus();
  };

  return (
    <li className="[&:first-child>button>div]:border-t-0">
      <button
        type="button"
        className="peer block w-full pl-4 text-left focus:outline-none focus-visible:ring"
        aria-expanded="false"
        aria-controls={panelId}
        onClick={open}
        ref={ob}
      >
        <div className="j:border-t j:border-button-neutral-outline q:border-b q:border-neutral-100 flex items-center justify-between overflow-hidden p-4 pl-0">
          <span className="truncate">{item.title}</span>
          <SvgIcon
            name="chevron-right"
            aria-hidden
            className="j:text-interactive-primary-enabled q:-m-0.5 shrink-0"
          />
        </div>
      </button>
      <div
        id={panelId}
        role="region"
        aria-label={item.title}
        className="invisible absolute top-0 z-[9999] h-full w-full translate-x-full bg-inherit bg-white transition-all peer-aria-expanded:visible peer-aria-expanded:translate-x-0"
      >
        <div className="j:border-button-neutral-outline q:border-neutral-100 relative flex items-center justify-between border-b">
          <button
            className="font-heading q:font-semibold flex items-center gap-x-3 overflow-hidden p-4 text-xl leading-6 focus:outline-none focus-visible:ring"
            onClick={close}
            aria-label={`Close ${item.title} submenu`}
          >
            <SvgIcon name="arrow-right" className="q:hidden rotate-180" aria-hidden />
            <SvgIcon
              name="chevron-large-right"
              className="j:hidden size-5 rotate-180"
              aria-hidden
            />{' '}
            <span className="truncate">{item.title}</span>
          </button>
          {item.titleLink && (
            <Link
              {...item.titleLink}
              className="j:text-interactive-primary-enabled q:text-interactive-primary-enabled flex shrink-0 items-center gap-x-1 p-4 text-sm hover:underline focus-visible:ring"
            >
              View all
              <SvgIcon name="arrow-right" className="j:hidden size-5" aria-hidden />
              <SvgIcon name="chevron-large-right" className="q:hidden size-4" aria-hidden />
            </Link>
          )}
        </div>
        <ul ref={container} className="space-y-0">
          {item.items.map((x, i) => (
            <NavigationItem key={i} item={x} />
          ))}
        </ul>
      </div>
    </li>
  );
};
