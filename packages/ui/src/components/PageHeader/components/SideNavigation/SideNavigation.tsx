'use client';

import { useEffect, useId, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useFocusVisible } from 'react-aria';
import { Button, Dialog, DialogTrigger, Heading, Modal, ModalOverlay } from 'react-aria-components';

import type { SessionUser } from '@qantasexperiences/auth/client';
import type { Channel, Tenant } from '@qantasexperiences/tenants';
import {
  getDisplayNameWithTitle,
  useLogin,
  useSignup,
  useSoftIdentity,
} from '@qantasexperiences/auth/client';
import { getTenantBasePath, getTenantTitle } from '@qantasexperiences/tenants';
import { cn } from '@qantasexperiences/utils/tailwind';

import type { UnknownNavigationItem } from '../../../../types';
import { Divider } from '../../../Divider';
import { SvgIcon } from '../../../SvgIcon';
import { TenantLogo } from '../../../TenantLogo';
import { getJetstarPrimaryNavLinks } from '../../utils';
import { MenuClickEvent } from '../MenuClickEvent';
import { QantasUniversalNavigation } from '../QantasUniversalNavigation/QantasUniversalNavigation';
import { NavigationItem } from './NavigationItem/NavigationItem';

interface QFFHeaderProps {
  qff?: SessionUser;
}

const QFFHeader = ({ qff }: QFFHeaderProps) => {
  const loginUrl = useLogin();
  const signUpUrl = useSignup();

  const prevQff = useSoftIdentity(qff);

  const name = getDisplayNameWithTitle(qff ?? prevQff);

  return (
    <div className="flex items-center justify-between border-b border-b-neutral-100 pl-4">
      <div className="overflow-hidden py-4">
        <Heading className="truncate text-xl font-semibold">{name ?? 'Welcome'}</Heading>
        {qff && <p className="text-text-secondary">{qff.pointBalance} pts</p>}
      </div>
      <div className="flex shrink-0 items-center">
        {qff && (
          <button className="text-interactive-primary-enabled inline-block p-4 font-semibold underline">
            Log out
          </button>
        )}
        {!qff && (
          <Link
            href={loginUrl}
            className="text-interactive-primary-enabled inline-block p-4 font-semibold underline"
          >
            Log In
          </Link>
        )}
        {!qff && !name && (
          <>
            <div className="inline-block h-6 w-px bg-neutral-100 font-semibold" aria-hidden />
            <Link
              href={signUpUrl}
              className="text-interactive-primary-enabled inline-block p-4 font-semibold underline"
            >
              Sign Up
            </Link>
          </>
        )}
      </div>
    </div>
  );
};

interface JetstarUniversalNavProps {
  channel: Channel;
}

const JetstarUniversalNav = ({ channel }: JetstarUniversalNavProps) => {
  return (
    <>
      <Divider orientation="horizontal" decorative color="subtle" className="my-2" />
      {getJetstarPrimaryNavLinks(channel).map(({ children, icon, href }) => (
        <li key={children}>
          <Link
            className="relative flex items-center gap-2 p-4 leading-tight font-semibold"
            href={href}
          >
            {icon}
            <span
              className={cn(
                channel === children.toLowerCase() &&
                  'after:bg-interactive-primary-enabled relative flex items-center gap-4 after:absolute after:-right-px after:-bottom-1 after:-left-px after:h-1 after:rounded',
              )}
            >
              {' '}
              {children}
            </span>
          </Link>
        </li>
      ))}
    </>
  );
};

const Navigation = ({
  navigation,
  tenant: { brand, channel },
  qff,
  close,
}: SideNavigationDialogProps & { close: () => void }) => {
  const handleFocus = (e: React.FocusEvent) => {
    e.preventDefault();
    if (
      e.relatedTarget?.nodeName === 'A' &&
      ((e.target.nodeName === 'A' && e.target.getAttribute('href')?.startsWith('#')) ||
        e.target.nodeName === 'BUTTON')
    ) {
      e.target
        .closest('ul')
        ?.querySelectorAll('[aria-expanded=true]')
        .forEach((el) => el.setAttribute('aria-expanded', 'false'));
    }
  };

  const pathname = usePathname();
  const prevPathname = useRef(pathname);

  useEffect(() => {
    if (pathname !== prevPathname.current) return close();
    prevPathname.current = pathname;
  }, [pathname, prevPathname, close]);

  return (
    <div className="relative flex min-h-full flex-col" onFocus={handleFocus}>
      {brand === 'qantas' && <QFFHeader qff={qff} />}
      <ul className="[&_svg]:size-6">
        {navigation.map((item, i) => (
          <NavigationItem key={i} item={item} />
        ))}
        {brand === 'jetstar' && <JetstarUniversalNav channel={channel} />}
      </ul>
      {brand === 'qantas' && <QantasUniversalNavigation className="global_features_mobile" />}
    </div>
  );
};

interface SideNavigationDialogProps {
  navigation: UnknownNavigationItem[];
  qff?: SessionUser;
  tenant: Tenant;
}

export const SideNavigation = ({ navigation, tenant, qff }: SideNavigationDialogProps) => {
  const { brand } = tenant;
  const dialogId = useId();

  const { isFocusVisible } = useFocusVisible({ autoFocus: true });

  const handleFocus = (e: React.FocusEvent) => {
    e.preventDefault();
    e.currentTarget.parentElement?.parentElement
      ?.querySelectorAll('[aria-expanded=true]')
      .forEach((el) => el.setAttribute('aria-expanded', 'false'));
  };

  return (
    <DialogTrigger>
      <Button className="focus-visible:ring-interactive-primary-enabled flex items-center gap-1 rounded-sm p-4 focus-visible:ring-2 focus-visible:outline-hidden focus-visible:ring-inset lg:hidden">
        {brand === 'qantas' ? (
          <>
            <span className="sr-only">Navigation</span>
            <SvgIcon name="burger" aria-hidden />
          </>
        ) : (
          <>
            <span className="font-semibold">Menu</span>
            <SvgIcon
              name="chevron-right"
              className="text-button-primary-enabled rotate-90"
              aria-hidden
            />
          </>
        )}
      </Button>
      <ModalOverlay isDismissable={true} className="fixed inset-0 z-10 bg-black/40 lg:hidden">
        <Modal className="fixed inset-0 h-[var(--visual-viewport-height)] w-full overflow-y-auto bg-white sm:w-96">
          <Dialog
            id={dialogId}
            className={cn(
              'flex min-h-full flex-col overflow-x-clip',
              !isFocusVisible && 'q:outline-hidden',
            )}
          >
            {({ close }) => (
              <>
                <div className="j:border-button-neutral-outline q:border-neutral-100 flex items-center justify-between border-b">
                  <Link
                    href={getTenantBasePath(tenant)}
                    className="j:py-3 q:py-3.5 px-4"
                    onFocus={handleFocus}
                  >
                    <span className="sr-only">{`${getTenantTitle(tenant)} home page`}</span>
                    <TenantLogo tenant={tenant} />
                  </Link>
                  <Button
                    onPress={close}
                    className="flex p-4"
                    aria-label="Dismiss"
                    onFocus={handleFocus}
                  >
                    <span className="sr-only">Close menu</span>
                    <SvgIcon name="close" aria-hidden className="size-6" />
                  </Button>
                </div>
                <Navigation navigation={navigation} tenant={tenant} qff={qff} close={close} />
                <MenuClickEvent containerId={dialogId} />
              </>
            )}
          </Dialog>
        </Modal>
      </ModalOverlay>
    </DialogTrigger>
  );
};
