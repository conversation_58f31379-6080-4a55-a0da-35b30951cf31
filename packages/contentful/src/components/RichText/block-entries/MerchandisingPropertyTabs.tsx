'use client';

import { Tab, <PERSON>b<PERSON><PERSON>, Tab<PERSON>anel, Tabs } from 'react-aria-components';

import type { Region } from '@qantasexperiences/utils/api';
import { PropertyCard } from '@qantasexperiences/ui';

import { ViewItemListEvent } from './ViewItemListEvent';

interface MerchandisingPropertyTabsProps {
  regions: Region[];
}

export const MerchandisingPropertyTabs = ({ regions }: MerchandisingPropertyTabsProps) => {
  if (regions.length === 0) {
    return <p className="text-center text-lg">Sorry, we currently have no offers in this area.</p>;
  }
  return (
    <Tabs className="rt text-base">
      <TabList className="mb-4 -ml-4 flex flex-wrap">
        {regions.map((r) => (
          <Tab
            key={r.regionId}
            id={r.regionId}
            className="rac-hover:decoration-red-600 rac-selected:decoration-red-600 cursor-pointer px-4 py-2 text-lg text-nowrap underline decoration-transparent decoration-[3px] underline-offset-[13px] transition-colors select-none hover:decoration-red-600 focus-visible:decoration-red-600"
          >
            {r.regionName}
          </Tab>
        ))}
      </TabList>
      {regions.map((r) => (
        <TabPanel key={r.regionId} id={r.regionId}>
          <ViewItemListEvent
            properties={r.properties}
            payload={{
              listName: 'Latest Deals Destination Page',
              location: r.regionName,
              payWith: 'cash',
            }}
          />
          {r.properties.length > 0 ? (
            <div className="grid auto-rows-[8.6875rem_auto_auto] gap-4 md:grid-cols-2 xl:grid-cols-4">
              {/* isExternal here will set the PropertyCard link to an external link */}
              {r.properties.map((p) => (
                <PropertyCard {...p} key={p.id} isExternal />
              ))}
            </div>
          ) : (
            <p className="text-center text-lg">Sorry, we currently have no offers in this area.</p>
          )}
        </TabPanel>
      ))}
    </Tabs>
  );
};
