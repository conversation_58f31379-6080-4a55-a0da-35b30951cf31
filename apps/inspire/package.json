{"name": "@qantasexperiences/inspire-app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "clean": "rm -rf .turbo node_modules", "delete": "pm2 delete ecosystem.config.cjs", "dev": "pm2 start ecosystem.config.cjs", "format:check": "prettier * **/* --check --ignore-unknown --ignore-path ../../.gitignore --no-error-on-unmatched-pattern", "lint:css": "stylelint **/*.css --ignore-path ../../.gitignore --allow-empty-input", "lint": "pnpm eslint --max-warnings=0 --cache --cache-location='node_modules/.cache/.eslintcache'", "lint:ci": "pnpm eslint --max-warnings=0", "start": "next start", "test:acceptance:ui": "playwright test --project=acceptance --ui --headed", "test:acceptance": "playwright test --project=acceptance --headed", "test:e2e:ui": "playwright test --project=e2e --ui --headed", "test:e2e": "playwright test --project=e2e --headed", "test:high-level": "playwright test --headed", "test:high-level:ui": "playwright test --ui --headed", "test:high-level:debug": "playwright test --debug --headed", "test:high-level:ci": "sh -c 'Xvfb :99 -screen 0 1280x720x24 > /dev/null 2>&1 & export DISPLAY=:99 && pnpm run test:high-level'", "test:high-level:snapshot": "playwright test --grep=snapshot --headed", "test:high-level:snapshot:update": "playwright test --grep=snapshot --update-snapshots --headed", "test:unit": "jest", "test:unit:ci": "jest --ci --coverage", "test:unit:watch": "jest --watch", "test:pa11y": "pa11y-ci", "typecheck": "tsc --noEmit"}, "dependencies": {"@contentful/rich-text-react-renderer": "^16.1.0", "@contentful/rich-text-types": "^17.1.0", "@next/third-parties": "^15.4.4", "@qantasexperiences/analytics": "workspace:*", "@qantasexperiences/auth": "workspace:*", "@qantasexperiences/contentful": "workspace:*", "@qantasexperiences/data": "workspace:*", "@qantasexperiences/env": "workspace:*", "@qantasexperiences/logger": "workspace:*", "@qantasexperiences/optimizely": "workspace:*", "@qantasexperiences/react-query": "workspace:*", "@qantasexperiences/sentry": "workspace:*", "@qantasexperiences/tenants": "workspace:*", "@qantasexperiences/theming": "workspace:*", "@qantasexperiences/ui": "workspace:*", "@qantasexperiences/utils": "workspace:*", "@sentry/nextjs": "^9.42.0", "@t3-oss/env-nextjs": "^0.13.8", "jiti": "^2.5.1", "lodash": "^4.17.21", "next": "^15.4.4", "react": "^19.1.0", "react-dom": "^19.1.0", "server-only": "^0.0.1", "zod": "^3.25.67"}, "devDependencies": {"@playwright/test": "1.54.1", "@qantasexperiences/code-style": "workspace:*", "@qantasexperiences/jest": "workspace:*", "@qantasexperiences/playwright-config": "workspace:*", "@qantasexperiences/tsconfig": "workspace:*", "@qantasexperiences/white-label": "workspace:*", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.1.11", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@testing-library/jest-dom": "^6.6.4", "@types/jest": "^30.0.0", "@types/jest-axe": "^3.5.9", "@types/lodash": "^4.17.19", "@types/node": "^22.16.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "jest-axe": "^10.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "pa11y-ci": "^4.0.0", "pm2": "^6.0.8", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}, "prettier": "@qantasexperiences/code-style/prettier"}