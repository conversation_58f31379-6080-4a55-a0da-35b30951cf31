import '@testing-library/jest-dom';
// Extend jest with axe a11y matcher (mirrors relationship + ui apps)
import { toHaveNoViolations } from 'jest-axe';

// Type casting due to jest-axe typings not matching jest v30 ExpectExtendMap generics fully
// (pattern consistent with other apps in monorepo)
// @ts-expect-error – jest-axe's matcher type is not fully compatible with Jest 30's ExpectExtendMap
expect.extend(toHaveNoViolations as never);
