import type { Metadata, ResolvingMetadata } from 'next';
import { draftMode } from 'next/headers';
import { notFound } from 'next/navigation';

import type { Tenant } from '@qantasexperiences/tenants';
import { ContentfulRichText } from '@qantasexperiences/contentful/components';
import { getDestinationPageBySlug } from '@qantasexperiences/contentful/queries';
import { ContentfulImage } from '@qantasexperiences/ui';

import './page.css';

import { TempDisclaimers } from './components/TempDisclaimers';

interface Props {
  params: Promise<Tenant & { slug: string[] }>;
}

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const { isEnabled } = await draftMode();

  const { slug, ...tenant } = await params;

  const destination = await getDestinationPageBySlug(tenant, slug.join('/'), isEnabled);

  if (!destination) return {};

  const { title, description, keywords, noFollow, noIndex, featuredImage } =
    destination.seoMetadata;

  const ogImageURL = new URL(featuredImage.src);
  ogImageURL.searchParams.set('fit', 'thumb');
  ogImageURL.searchParams.set('fm', 'jpg');
  ogImageURL.searchParams.set('w', '1200');
  ogImageURL.searchParams.set('h', '630');

  const ogImage = { width: 1200, height: 630, url: ogImageURL };

  const twitterImageURL = new URL(ogImageURL);
  twitterImageURL.searchParams.set('h', '675');

  const twImage = { width: 1200, height: 675, url: ogImageURL };

  return {
    title,
    description,
    keywords,
    robots: {
      follow: !noFollow,
      index: !noIndex,
      googleBot: { follow: !noFollow, index: !noIndex },
    },
    openGraph: {
      url: `/${destination.slug}`,
      title,
      description,
      images: ogImage,
      type: 'website',
      siteName: (await parent).openGraph?.siteName,
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: twImage,
    },
  };
}

export default async function DestinationPage({ params }: Props) {
  const { isEnabled } = await draftMode();

  const { slug, ...tenant } = await params;
  const destination = await getDestinationPageBySlug(tenant, slug.join('/'), isEnabled);

  if (!destination) return notFound();

  return (
    // main landmark already has skip link target id="main-content" from global skip link implementation
    <main id="main-content" role="main" aria-labelledby="destination-page-title">
      {/* Disclaimers are supplementary content */}
      <TempDisclaimers />
      <article className="wysiwyg text-lg" aria-describedby="destination-intro" role="article">
        <div className="rt-fb grid items-end justify-items-center *:col-end-1 *:row-end-1">
          <ContentfulImage
            {...destination.heroImage}
            // Ensure a non-empty alt if provided, fall back to hero title for context, else decorative
            alt={destination.heroImage?.alt || destination.heroTitle || ''}
            priority={true}
            className="aspect-[2/1] max-h-[27.5rem] w-full object-cover sm:aspect-[5/2]"
          />
          <h1
            id="destination-page-title"
            className="bg-white px-8 pt-5 text-[2rem] leading-none font-semibold md:text-5xl"
          >
            {destination.heroTitle}
          </h1>
        </div>
        <p id="destination-intro" className="text-center text-balance md:text-2xl">
          {destination.heroDescription}
        </p>
        <ContentfulRichText content={destination.content} />
      </article>
    </main>
  );
}
