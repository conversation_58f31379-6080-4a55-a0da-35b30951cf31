import '@testing-library/jest-dom';

import React from 'react';
import { render, screen } from '@testing-library/react';
import { axe } from 'jest-axe';

import DestinationPage, { generateMetadata } from './page';

// Mock contentful query
jest.mock('@qantasexperiences/contentful/queries', () => ({
  getDestinationPageBySlug: jest.fn().mockResolvedValue({
    slug: 'test-destination',
    heroTitle: 'Accessible Destination',
    heroDescription: 'A description of the accessible destination',
    heroImage: { src: 'https://example.com/image.jpg', alt: 'Scenic view' },
    content: [],
    seoMetadata: {
      title: 'Accessible Destination',
      description: 'Description',
      keywords: ['accessible', 'destination'],
      noFollow: false,
      noIndex: false,
      featuredImage: { src: 'https://example.com/featured.jpg' },
    },
  }),
}));

// Draft mode mock
jest.mock('next/headers', () => ({
  draftMode: () => ({ isEnabled: false }),
}));

describe('DestinationPage accessibility', () => {
  it('has no detectable a11y violations', async () => {
    const paramsPromise = Promise.resolve({
      brand: 'qantas',
      channel: 'hotels',
      slug: ['test-destination'],
    });
    const { container } = render(<DestinationPage params={paramsPromise as any} />);
    // Wait for heading to appear
    await screen.findByRole('heading', { level: 1, name: /Accessible Destination/i });
    const results = await axe(container, { rules: { region: { enabled: true } } });
    expect(results).toHaveNoViolations();
  });
});
