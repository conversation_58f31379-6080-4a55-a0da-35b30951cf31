import type { Locator, <PERSON> } from '@playwright/test';

export class BookingDetailsPage {
  readonly bookingRefElement: Locator;
  readonly closeSessionButton: Locator;
  readonly downloadTaxInvoiceButton: Locator;
  readonly downloadPaymentReceiptButton: Locator; // TODO: use after https://qantas.atlassian.net/browse/GARD-538 is implemented (the filename becomes payment-receipt-<booking-ref>.pdf for EPS bookings)
  readonly downloadWalletPassButton: Locator;
  readonly qffDropdown: Locator;
  readonly logoutButton: Locator;
  readonly backToBookingsListButton: Locator;
  constructor(page: Page) {
    this.bookingRefElement = page.getByTestId('desktop-booking-ref');
    this.closeSessionButton = page.getByRole('button', { name: 'Close session ' });
    this.downloadTaxInvoiceButton = page.getByRole('button', { name: 'Download invoice' });
    this.downloadPaymentReceiptButton = page.getByRole('button', { name: 'Download receipt' });
    this.downloadWalletPassButton = page.getByRole('button', {
      name: 'Download Apple Wallet Pass',
    });
    this.qffDropdown = page.getByTestId('qff-menu-tab');
    this.logoutButton = page.getByRole('button', { name: 'LOG OUT' });
    this.backToBookingsListButton = page.getByRole('link', { name: 'Your hotel bookings' });
  }
}
