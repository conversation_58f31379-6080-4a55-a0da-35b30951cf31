import type { Locator, <PERSON> } from '@playwright/test';

export class BookingListPage {
  readonly upcomingBookingsButton: Locator;
  readonly pastBookingsButton: Locator;
  readonly cancelledBookingsButton: Locator;
  readonly upcomingBookingList: Lo<PERSON>or;
  readonly pastBookingList: Locator;
  readonly cancelledBookingList: Locator;
  readonly firstViewDetailsLink: Locator;

  constructor(page: Page) {
    this.upcomingBookingsButton = page.locator('label[value="upcoming"]');
    this.pastBookingsButton = page.locator('label[value="past"]');
    this.cancelledBookingsButton = page.locator('label[value="cancelled"]');
    this.upcomingBookingList = page.locator('[data-testid="upcoming-booking-list"]');
    this.pastBookingList = page.locator('[data-testid="past-booking-list"]');
    this.cancelledBookingList = page.locator('[data-testid="cancelled-booking-list"]');
    this.firstViewDetailsLink = page.getByRole('link', { name: 'View details' }).first();
  }
}
