import { expect, test } from '../fixtures/loginFixture';
import { BookingDetailsPage } from '../pageObjects/BookingDetailsPage';
import { BookingListPage } from '../pageObjects/BookingListPage';

test.describe('Booking List Page in Self Service', () => {
  test('navigates between cancelled bookings list and booking details pages', async ({
    page,
    performQFFLogin,
  }) => {
    await performQFFLogin();
    await expect(page).toHaveTitle('Your hotel bookings | Qantas Hotels', { timeout: 20000 });

    const bookingListPage = new BookingListPage(page);
    const bookingDetailsPage = new BookingDetailsPage(page);

    await bookingListPage.cancelledBookingsButton.click();
    await expect(page).toHaveURL(/bookings\?filter=cancelled/);
    await expect(bookingListPage.cancelledBookingList).toBeVisible();

    await bookingListPage.firstViewDetailsLink.click();
    await expect(page).toHaveURL(/\/bookings\/[a-fA-F0-9-]{36}$/);

    await bookingDetailsPage.backToBookingsListButton.click();
    await expect(bookingListPage.upcomingBookingsButton).toBeVisible();
  });

  test('navigates between past bookings list and booking details pages', async ({
    page,
    performQFFLogin,
  }) => {
    await performQFFLogin();
    await expect(page).toHaveTitle('Your hotel bookings | Qantas Hotels', { timeout: 20000 });

    const bookingListPage = new BookingListPage(page);
    const bookingDetailsPage = new BookingDetailsPage(page);

    await bookingListPage.pastBookingsButton.click();
    await expect(page).toHaveURL(/bookings\?filter=past/);
    await expect(bookingListPage.pastBookingList).toBeVisible();

    await bookingListPage.firstViewDetailsLink.click();
    await expect(page).toHaveURL(/\/bookings\/[a-fA-F0-9-]{36}$/);

    await bookingDetailsPage.backToBookingsListButton.click();
    await expect(bookingListPage.upcomingBookingsButton).toBeVisible();
  });

  // TODO: This test currently uses a booking for august 2026. Yet to be future proofed with creating a new booking for a future date.
  test('navigates between upcoming bookings list and booking details pages', async ({
    page,
    performQFFLogin,
  }) => {
    await performQFFLogin();
    await expect(page).toHaveTitle('Your hotel bookings | Qantas Hotels', { timeout: 20000 });

    const bookingListPage = new BookingListPage(page);
    const bookingDetailsPage = new BookingDetailsPage(page);

    await bookingListPage.upcomingBookingsButton.click();
    await expect(page).toHaveURL(/bookings\?filter=upcoming/);
    await expect(bookingListPage.upcomingBookingList).toBeVisible();

    await bookingListPage.firstViewDetailsLink.click();
    await expect(page).toHaveURL(/\/bookings\/[a-fA-F0-9-]{36}$/);

    await bookingDetailsPage.backToBookingsListButton.click();
    await expect(bookingListPage.upcomingBookingsButton).toBeVisible();
  });
});
